import CommonHeader from "../../../components/layout/common_header";
import CommonFooter from "../../../components/layout/common_footer";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { useMemo, useState } from "react";
import { Table } from "../../../components/datatable";
import { PaginationComponent } from "../../../components/pagination";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { useGetUserStatusQuery } from "../../../feature/api/statusApiSlice";
import FormikField from "../../../components/formikField";
import { useEditCustomerMutation } from "../../../feature/api/customerDataApiSlice";
import { useLocation, useNavigate } from "react-router-dom";
import { Modal } from "react-bootstrap";
import WebLoader from "../../../components/webLoader";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import { useDeleteCustomerContactsMutation, useEditCustomerContactsMutation, useGetCustomerContactsListQuery } from "../../../feature/api/customerContactsDataApiSlice";
import useConfirmDelete from "../../../hooks/useConfirmDelete";

const validation = yup.object().shape({
  customer_name: yup.string().required().label("Customer Name"),
  phone_code: yup.string().required().label("Phone Code"),
  phone: yup.string().required().label("Phone"),
  email: yup.string().email().label("Email"),
  qid: yup.string().length(11).required().label("Qatar ID"),
  billing_address: yup.string().label("Billing Address"),
  shipping_address: yup.string().label("Shipping Address"),
  tax_id: yup.string().label("Tax ID"),
  payment_terms: yup.string().label("Payment Terms"),
  credit_limit: yup.string().label("Credit Limit"),
  status: yup.string().required().label("Status"),
  remarks: yup.string().label("Remarks"),
  branch_id: yup.string().required().label("Branch"),
});
export default function ViewCustomer() {
  const navigate = useNavigate(); // Initialize the navigation hook
  const { state } = useLocation();
  const customerData = state;
  const activePage = "Customers Master";
  const linkHref = "/dashboard";

  const initialValues = {
    customer_name: customerData?.customer_name || null,
    phone_code: customerData?.phone_code || "",
    phone: customerData?.phone || "",
    email: customerData?.email || "",
    qid: customerData?.qid || "",
    billing_address: customerData?.billing_address || "",
    shipping_address: customerData?.shipping_address || "",
    tax_id: customerData?.tax_id || "",
    payment_terms: customerData?.payment_terms || "",
    credit_limit: customerData?.credit_limit || "",
    status: customerData?.status || "",
    remarks: customerData?.remarks || "",
    branch_id: customerData?.branch_id || "",
  };

  /* **************** Start list User Status ******************* */
  const userStatusData = useGetUserStatusQuery();
  const userStatusDataList = userStatusData?.data?.data || [];
  const userStatusList = userStatusDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list User Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start Edit Customer ******************* */
  const [handleEditCustomerApi, { isLoading: isEditCustomerLoading }] =
    useEditCustomerMutation();
  const handleSubmit = async (body) => {
    try {
      const createBody = {
        ...body,
        customer_id: parseInt(customerData?.id),
        branch_id: parseInt(body.branch_id),
        status: parseInt(body.status),
      };
      const resp = await handleEditCustomerApi(createBody).unwrap();
      handleApiSuccess(resp);
      navigate("/customers");
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Customer ******************* */

  /* **************** Start Customer Contacts Section ************** */

  const contactValidation = yup.object().shape({
    contact_name: yup.string().required().label("Contact Name"),
    phone_code: yup.string().required().label("Phone Code"),
    phone: yup.string().required().label("Phone"),
    email: yup.string().email().label("Email"),
    remarks: yup.string().label("Remarks"),
  });

    /* **************** Start Contact Data Edit Model ******************* */
    const [showEditModel, setShowEditModel] = useState(false);
    const handleContactEditModelClose = () => setShowEditModel(false);
    const handleContactEditModelShow = () => setShowEditModel(true);
    /* **************** End Contact Data Edit Model ******************* */

    /* **************** Start list all Customer Contact ******************* */

    const [currentContactPage, setCurrentContactPage] = useState(1);
    const [filterContactsTableKeywords, setFilterKeywords] = useState("");

    /* **************** Start list all Customer Contacts ******************* */

    const { data: customerContactsData, isLoading: isLoadingContacts } = useGetCustomerContactsListQuery({
      page: currentContactPage,
      customer_id: parseInt(customerData?.id),
      keywords: filterContactsTableKeywords,
    });
    const customerContactsList = useMemo(() => {
      if (!customerContactsData?.data.list?.length) {
        if (currentContactPage > 1) setCurrentContactPage((current) => current - 1);
        return [];
      }
      return customerContactsData?.data?.list;
    }, [currentContactPage, customerContactsData?.data.list]);
  
    const contactPageData = useMemo(
      () => customerContactsData?.data?.page ?? null,
      [customerContactsData]
    );
  
    /* **************** End list all Customer Contacts ******************* */

    /* **************** Start Filter ***************** */
    const handleKeywordsFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterKeywords(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    /* **************** End Filter ***************** */
    const fetchContactData = async (page) => {
      try {
        setCurrentContactPage(page); // Update the page state
      } catch (error) {
        handleApiErrors(error);
      }
    };

    /* **************** Start Delete Customer Contact ******************* */
    
      const { showSimpleConfirm } = useConfirmDelete({
            title: 'Delete Customer Contact?',
            text: 'Are you sure you want to delete this customer contact?',
            confirmButtonText: 'Yes, delete customer contact!',
            cancelButtonText: 'Cancel'
        });
    
      const [handledeleteDataApi, { isLoading: isDeleteContactLoading }] =
        useDeleteCustomerContactsMutation();
      const onDeleteContactDataHandler = async (id) => {
        const confirmed = await showSimpleConfirm();
          if (confirmed) {
            try {
              const body = { contact_id: id };
              const resp = await handledeleteDataApi(body).unwrap();
              handleApiSuccess(resp);
            } catch (error) {
              handleApiErrors(error);
            }
          }
      };

      /* **************** End Delete Customer Contact ******************* */

    const [editContactValues, setEditContactValues] = useState({
      branch_id: "",
      customer_id: "",
      contact_name: "",
      phone_code: "",
      phone: "",
      email: "",
      remarks: "",
    });
    const handleOpenModal = (values) => {
      setEditContactValues(values);
      handleContactEditModelShow();
    };
    
    const onEditContactDataDetailsHandler = (d) => {
      handleOpenModal({
        customer_id: d?.customer_id ? parseInt(d.customer_id) : "",
        id: d?.id || "",
        contact_name: d?.contact_name || "",
        phone_code: d?.phone_code || "",
        phone: d?.phone || "",
        email: d?.email || "",
        remarks: d?.remarks || "",
      });
    };
  
    const [handleEditDataApi, { isLoading: isEditContactLoading }] =
      useEditCustomerContactsMutation();
    const DataUpdateFormSubmit = async (body) => {
      try {
        const updatedBody = {
          ...body,
          contact_id: parseInt(body.id),
          customer_id: parseInt(body.customer_id),
        };      
        const resp = await handleEditDataApi(updatedBody).unwrap();
        handleApiSuccess(resp);
        handleContactEditModelClose();
      } catch (error) {
        handleApiErrors(error);
      }
    };

  /* *************** End Customer Contacts Section **************** */

  /* **************** Web Loader  ******************* */
  if (isEditCustomerLoading || isLoadingContacts || isEditContactLoading || isDeleteContactLoading) return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  ></div>
                  <ul
                    className="nav nav-pills user-profile-tab"
                    id="pills-tab"
                    role="tablist"
                  >
                    <li className="nav-item" role="presentation">
                      <button
                        className="nav-link position-relative rounded-0 active d-flex align-items-center justify-content-center bg-transparent fs-3 py-3"
                        id="pills-account-tab"
                        data-bs-toggle="pill"
                        data-bs-target="#pills-account"
                        type="button"
                        role="tab"
                        aria-controls="pills-account"
                        aria-selected="true"
                      >
                        <i className="ti ti-user-circle me-2 fs-6"></i>
                        <span className="d-none d-md-block">Customer Details</span>
                      </button>
                    </li>
                    <li className="nav-item" role="presentation">
                      <button
                        className="nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3"
                        id="pills-contacts-tab"
                        data-bs-toggle="pill"
                        data-bs-target="#pills-contacts"
                        type="button"
                        role="tab"
                        aria-controls="pills-contacts"
                        aria-selected="false"
                      >
                        <i className="ti ti-phone me-2 fs-6"></i>
                        <span className="d-none d-md-block">Contacts</span>
                      </button>
                    </li>
                    <li className="nav-item" role="presentation">
                      <button
                        className="nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3"
                        id="pills-bills-tab"
                        data-bs-toggle="pill"
                        data-bs-target="#pills-bills"
                        type="button"
                        role="tab"
                        aria-controls="pills-bills"
                        aria-selected="false"
                      >
                        <i className="ti ti-note me-2 fs-6"></i>
                        <span className="d-none d-md-block">Notes</span>
                      </button>
                    </li>
                    <li className="nav-item" role="presentation">
                      <button
                        className="nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3"
                        id="pills-security-tab"
                        data-bs-toggle="pill"
                        data-bs-target="#pills-security"
                        type="button"
                        role="tab"
                        aria-controls="pills-security"
                        aria-selected="false"
                      >
                        <i className="ti ti-paperclip me-2 fs-6"></i>
                        <span className="d-none d-md-block">Attachments</span>
                      </button>
                    </li>
                  </ul>
                  <div className="card-body">
                  <div className="tab-content" id="pills-tabContent">
                    {/* Start Customer Details Tab */}
                  <div className="tab-pane fade show active" id="pills-account" role="tabpanel" aria-labelledby="pills-account-tab" tabIndex="0">
                    <div className="row">
                        <div className="col-lg-12 d-flex align-items-stretch">
                          <div className="card w-100 border position-relative overflow-hidden mb-0">
                            <div className="card-body p-4">
                              <h4 className="card-title">Customer Details</h4>
                              <p className="card-subtitle mb-4">
                                To update Customer, add details and save from here
                              </p>
                              <Formik
                                initialValues={initialValues}
                                validationSchema={validation}
                                onSubmit={handleSubmit}
                              >
                                <Form
                                  name="product-create"
                                  className="needs-validation"
                                  autoComplete="off"
                                  encType="multipart/form-data"                      
                                >
                                  <div className="row">     
                                  <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_id"
                                          className="form-label"
                                        >
                                          Branch 
                                          <span className="un-validation">(*)</span>
                                        </label>
                                        <FormikField
                                          name="branch_id"
                                          id="branch_id"
                                          className="form-select"
                                          type="select"
                                          options={branchesList}
                                        />
                                      </div>
                                    </div>                             
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="customer_name"
                                          className="form-label"
                                        >
                                          Customer Name
                                          <span className="un-validation">(*)</span>
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="customer_name"
                                          id="customer_name"
                                          placeholder="Customer Name *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label className="form-label">
                                          Phone Number{" "}
                                          <sp className="un-validation">*</sp>
                                        </label>
                                        <div className="row">
                                          <div className="col-4">
                                            <FormikField
                                              name="phone_code"
                                              id="phone_code"
                                              className="form-select"
                                              type="select"
                                              options={[
                                                {
                                                  value: "+974",
                                                  label: "+974 - Qatar",
                                                },
                                              ]}
                                            />
                                          </div>
                                          <div className="col-8">
                                            <FormikField
                                              type="number"
                                              name="phone"
                                              id="phone"
                                              placeholder="Phone Number *"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="email"
                                          className="form-label"                                
                                        >
                                          Email                                      
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="email"
                                          id="email"
                                          placeholder="Email"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_email"
                                          className="form-label"
                                        >
                                          Qatar ID{" "}
                                          <span className="un-validation">*</span>
                                        </label>
                                        <FormikField
                                          type="number"
                                          name="qid"
                                          id="qid"
                                          placeholder=" Qatar ID *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="billing_address"
                                          className="form-label"
                                        >
                                          Billing Address
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="billing_address"
                                          id="billing_address"
                                          placeholder="Billing Address"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="shipping_address"
                                          className="form-label"
                                        >
                                          Shipping Address
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="shipping_address"
                                          id="shipping_address"
                                          placeholder="Shipping Address"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>  
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="tax_id"
                                          className="form-label"
                                        >
                                          Tax ID
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="tax_id"
                                          id="tax_id"
                                          placeholder="Tax ID"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="payment_terms"
                                          className="form-label"
                                        >
                                          Payment Terms
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="payment_terms"
                                          id="payment_terms"
                                          placeholder="Payment Terms"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="credit_limit"
                                          className="form-label"
                                        >
                                          Credit Limit
                                        </label>
                                        <FormikField
                                          type="number"
                                          name="credit_limit"
                                          id="credit_limit"
                                          placeholder="Credit Limit"
                                          autoComplete="off"
                                          className="form-control"
                                          step="0.01"
                                        />
                                      </div>
                                    </div> 
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="remarks"
                                          className="form-label"
                                        >
                                          Remarks
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="remarks"
                                          id="remarks"
                                          placeholder="Remarks"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>                             
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="status"
                                          className="form-label"
                                        >
                                          Status
                                          <span className="un-validation">(*)</span>
                                        </label>
                                        <FormikField
                                          name="status"
                                          id="status"
                                          className="form-select"
                                          type="select"
                                          options={userStatusList}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-12">
                                      <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                        <button
                                          className="btn btn-primary"
                                          type="submit"
                                        >
                                          Edit Customer
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </Form>
                              </Formik>
                            </div>
                          </div>
                        </div>
                    </div>
                  </div>
                    {/* End Customer Details Tab */}

                    {/* Start Customer Contacts Tab */}
                  <div className="tab-pane fade" id="pills-contacts" role="tabpanel" aria-labelledby="pills-contacts-tab" tabIndex="0" onClick="">
                    <div className="card-body p-4">
                      <div className="table-responsive">
                        <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                           <div className="d-flex  gap-6">                    
                          </div>
                          <div className="position-relative">
                            <input
                              type="text"
                              className="form-control search-chat py-2 ps-5"
                              id="text-srh"
                              onChange={handleKeywordsFilter}
                              placeholder="Keyword Search..."
                              value={filterContactsTableKeywords}
                            />
                            <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                          </div>
                        </div>
                        <Table
                          headCells={[
                            { key: "sel_id", label: "#", align: "left" },
                            {
                              key: "contact_name",
                              label: "Contact Name",
                              align: "left",
                            },
                            {
                              key: "phone",
                              label: "Phone",
                              align: "left",
                            },
                            {
                              key: "email",
                              label: "Email",
                              align: "left",
                            },
                            {
                              key: "created_at",
                              label: "Created At",
                              align: "left",
                            },
                          ]}
                          data={customerContactsList}
                          onDeleteHandler={onDeleteContactDataHandler}
                          onEditHandler={onEditContactDataDetailsHandler}
                        />
                        <PaginationComponent
                          totalCount={contactPageData?.total_count}
                          pageSize={contactPageData?.page_size}
                          currentPage={currentContactPage}
                          setCurrentPage={setCurrentContactPage}
                          onPageChange={fetchContactData}
                        />
                      </div>
                    </div>            
                  </div>

                  {/* Customer Contacts Edit Modal */}
                  
                  <Modal
                    show={showEditModel}
                    onHide={handleContactEditModelClose}
                    backdrop="static"
                    keyboard={false}
                    aria-labelledby="staticBackdropLabel"
                    aria-hidden="true"
                  >
                    <Modal.Header closeButton>
                      <Modal.Title> Update Customer Contact</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                      <Formik
                        initialValues={editContactValues}
                        enableReinitialize
                        validationSchema={contactValidation}
                        onSubmit={DataUpdateFormSubmit}
                      >
                        {({ handleSubmit}) => {              
                          return (
                          <Form
                            name="role-update"
                            className="needs-validation"
                            autoComplete="off"
                          >
                            <FormikField
                              type="hidden"
                              name="id"
                              id="id"
                              autoComplete="off"
                              className="form-control"
                            />
                            <div className="modal-body">
                              <div className="row">                                   
                                <div className="col-lg-12">
                                  <div className="mb-3">
                                    <label htmlFor="contact_name" className="form-label">
                                      Contact Name
                                      <span className="un-validation">(*)</span>
                                    </label>
                                    <FormikField
                                      type="text"
                                      name="contact_name"
                                      id="contact_name"
                                      placeholder="Contact Name"
                                      autoComplete="off"
                                      className="form-control"
                                    />
                                  </div>
                                </div>
                                <div className="col-lg-12">
                                  <div className="mb-3">
                                    <label htmlFor="phone" className="form-label">
                                      Phone Number{" "}
                                      <span className="un-validation">(*)</span> :
                                    </label>
                                    <div className="row">
                                      <div className="col-4">
                                        <FormikField
                                          name="phone_code"
                                          id="phone_code"
                                          className="form-select"
                                          type="select"
                                          options={[
                                            {
                                              value: "+974",
                                              label: "+974 - Qatar",
                                            },
                                          ]}
                                        />
                                      </div>
                                      <div className="col-8">
                                        <FormikField
                                          type="number"
                                          name="phone"
                                          id="phone"
                                      placeholder="Phone Number"
                                      autoComplete="off"
                                      className="form-control"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="col-lg-12">
                                  <div className="mb-3">
                                    <label htmlFor="email" className="form-label">
                                      Email
                                    </label>
                                    <FormikField
                                      type="text"
                                      name="email"
                                      id="email"
                                      placeholder="Email"
                                      autoComplete="off"
                                      className="form-control"
                                    />
                                  </div>
                                </div>
                                <div className="col-lg-12">
                                  <div className="mb-3">
                                    <label htmlFor="remarks" className="form-label">
                                      Remarks
                                    </label>
                                    <FormikField
                                      type="textarea"
                                      name="remarks"
                                      id="remarks"
                                      placeholder="Remarks"
                                      autoComplete="off"
                                      className="form-control"
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="modal-footer">
                              <button
                                type="button"
                                className="btn bg-danger-subtle text-danger  waves-effect text-start ClassModel"
                                data-bs-dismiss="modal"
                                onClick={handleContactEditModelClose}
                              >
                                Close
                              </button>
                              <button className="btn btn-primary" onClick={handleSubmit}>
                                Update Customer Contact
                              </button>
                            </div>
                          </Form>
                          );
                        }}
                      </Formik>
                    </Modal.Body>
                    {/* <Modal.Footer></Modal.Footer> */}
                    </Modal>
                  {/* End Customer Contacts Edit Modal */}
                  {/* End Customer Contacts Tab */}
                </div>
                </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
